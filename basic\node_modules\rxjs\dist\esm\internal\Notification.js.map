{"version": 3, "file": "Notification.js", "sourceRoot": "", "sources": ["../../../src/internal/Notification.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAC3C,OAAO,EAAE,EAAE,EAAE,MAAM,iBAAiB,CAAC;AACrC,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAO/C,MAAM,CAAN,IAAY,gBAIX;AAJD,WAAY,gBAAgB;IAC1B,8BAAU,CAAA;IACV,+BAAW,CAAA;IACX,kCAAc,CAAA;AAChB,CAAC,EAJW,gBAAgB,KAAhB,gBAAgB,QAI3B;AAkBD,MAAM,OAAO,YAAY;IA6BvB,YAA4B,IAAqB,EAAkB,KAAS,EAAkB,KAAW;QAA7E,SAAI,GAAJ,IAAI,CAAiB;QAAkB,UAAK,GAAL,KAAK,CAAI;QAAkB,UAAK,GAAL,KAAK,CAAM;QACvG,IAAI,CAAC,QAAQ,GAAG,IAAI,KAAK,GAAG,CAAC;IAC/B,CAAC;IAQD,OAAO,CAAC,QAA4B;QAClC,OAAO,mBAAmB,CAAC,IAAiC,EAAE,QAAQ,CAAC,CAAC;IAC1E,CAAC;IA4BD,EAAE,CAAC,WAA+B,EAAE,YAAiC,EAAE,eAA4B;QACjG,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QACpC,OAAO,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAG,KAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAG,KAAK,CAAC,CAAC,CAAC,CAAC,eAAe,aAAf,eAAe,uBAAf,eAAe,EAAI,CAAC;IAC3G,CAAC;IAqCD,MAAM,CAAC,cAAyD,EAAE,KAA0B,EAAE,QAAqB;;QACjH,OAAO,UAAU,CAAC,MAAC,cAAsB,0CAAE,IAAI,CAAC;YAC9C,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAoC,CAAC;YACpD,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,cAAoC,EAAE,KAAY,EAAE,QAAe,CAAC,CAAC;IACnF,CAAC;IASD,YAAY;QACV,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QAEpC,MAAM,MAAM,GACV,IAAI,KAAK,GAAG;YACV,CAAC;gBACC,EAAE,CAAC,KAAM,CAAC;YACZ,CAAC;gBACD,IAAI,KAAK,GAAG;oBACZ,CAAC;wBACC,UAAU,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;oBACzB,CAAC;wBACD,IAAI,KAAK,GAAG;4BACZ,CAAC;gCACC,KAAK;4BACP,CAAC;gCACC,CAAC,CAAC;QACR,IAAI,CAAC,MAAM,EAAE;YAIX,MAAM,IAAI,SAAS,CAAC,gCAAgC,IAAI,EAAE,CAAC,CAAC;SAC7D;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAaD,MAAM,CAAC,UAAU,CAAI,KAAQ;QAC3B,OAAO,IAAI,YAAY,CAAC,GAAG,EAAE,KAAK,CAA0C,CAAC;IAC/E,CAAC;IAYD,MAAM,CAAC,WAAW,CAAC,GAAS;QAC1B,OAAO,IAAI,YAAY,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,CAA4C,CAAC;IAC1F,CAAC;IAUD,MAAM,CAAC,cAAc;QACnB,OAAO,YAAY,CAAC,oBAAoB,CAAC;IAC3C,CAAC;;AAvCc,iCAAoB,GAAG,IAAI,YAAY,CAAC,GAAG,CAA+C,CAAC;AAiD5G,MAAM,UAAU,mBAAmB,CAAI,YAAuC,EAAE,QAA4B;;IAC1G,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,YAAmB,CAAC;IACnD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC;KAC7D;IACD,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,MAAA,QAAQ,CAAC,IAAI,+CAAb,QAAQ,EAAQ,KAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,MAAA,QAAQ,CAAC,KAAK,+CAAd,QAAQ,EAAS,KAAK,CAAC,CAAC,CAAC,CAAC,MAAA,QAAQ,CAAC,QAAQ,+CAAjB,QAAQ,CAAa,CAAC;AAC1G,CAAC"}