{"version": 3, "file": "AsyncAction.js", "sourceRoot": "", "sources": ["../../../../src/internal/scheduler/AsyncAction.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAIlC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAG9C,MAAM,OAAO,WAAe,SAAQ,MAAS;IAO3C,YAAsB,SAAyB,EAAY,IAAmD;QAC5G,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QADH,cAAS,GAAT,SAAS,CAAgB;QAAY,SAAI,GAAJ,IAAI,CAA+C;QAFpG,YAAO,GAAY,KAAK,CAAC;IAInC,CAAC;IAEM,QAAQ,CAAC,KAAS,EAAE,QAAgB,CAAC;;QAC1C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,IAAI,CAAC;SACb;QAGD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAuBjC,IAAI,EAAE,IAAI,IAAI,EAAE;YACd,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;SACrD;QAID,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,CAAC,EAAE,GAAG,MAAA,IAAI,CAAC,EAAE,mCAAI,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAEpE,OAAO,IAAI,CAAC;IACd,CAAC;IAES,cAAc,CAAC,SAAyB,EAAE,GAAiB,EAAE,QAAgB,CAAC;QACtF,OAAO,gBAAgB,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IACpF,CAAC;IAES,cAAc,CAAC,UAA0B,EAAE,EAAgB,EAAE,QAAuB,CAAC;QAE7F,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;YACnE,OAAO,EAAE,CAAC;SACX;QAGD,IAAI,EAAE,IAAI,IAAI,EAAE;YACd,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;SACpC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAKM,OAAO,CAAC,KAAQ,EAAE,KAAa;QACpC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SAClD;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC1C,IAAI,KAAK,EAAE;YACT,OAAO,KAAK,CAAC;SACd;aAAM,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,EAAE;YAcpD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;SAC9D;IACH,CAAC;IAES,QAAQ,CAAC,KAAQ,EAAE,MAAc;QACzC,IAAI,OAAO,GAAY,KAAK,CAAC;QAC7B,IAAI,UAAe,CAAC;QACpB,IAAI;YACF,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAClB;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,GAAG,IAAI,CAAC;YAIf,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;SACtE;QACD,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,UAAU,CAAC;SACnB;IACH,CAAC;IAED,WAAW;QACT,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;YAC/B,MAAM,EAAE,OAAO,EAAE,GAAG,SAAS,CAAC;YAE9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,GAAG,IAAK,CAAC;YAChD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YAErB,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACzB,IAAI,EAAE,IAAI,IAAI,EAAE;gBACd,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;aACpD;YAED,IAAI,CAAC,KAAK,GAAG,IAAK,CAAC;YACnB,KAAK,CAAC,WAAW,EAAE,CAAC;SACrB;IACH,CAAC;CACF"}