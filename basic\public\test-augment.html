<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test Augment - Déplacement</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .augment-transition {
            transition: all 0.5s ease-in-out;
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 p-6">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">Démonstration - Déplacement du composant Augment</h1>
        
        <!-- Composant Augment déplaçable -->
        <div class="w-full mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-medium">Interface Layout</h2>
                <button id="toggleAugment" class="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium">
                    Déplacer Augment à gauche
                </button>
            </div>
            
            <div id="layoutContainer" class="flex gap-4 min-h-[400px] border-2 border-gray-300 dark:border-gray-600 rounded-xl p-6 bg-white dark:bg-gray-800">
                <!-- Zone principale -->
                <div class="flex-1 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-4">📄 Contenu Principal</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">Ceci est la zone de contenu principal de votre application Laravel.</p>
                    <div class="space-y-3">
                        <div class="bg-white dark:bg-gray-600 p-3 rounded border">
                            <h4 class="font-medium">Article 1</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-300">Contenu de l'article...</p>
                        </div>
                        <div class="bg-white dark:bg-gray-600 p-3 rounded border">
                            <h4 class="font-medium">Article 2</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-300">Contenu de l'article...</p>
                        </div>
                        <div class="bg-white dark:bg-gray-600 p-3 rounded border">
                            <h4 class="font-medium">Article 3</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-300">Contenu de l'article...</p>
                        </div>
                    </div>
                </div>
                
                <!-- Composant Augment (initialement à droite) -->
                <div id="augmentComponent" class="w-80 bg-gradient-to-br from-red-600 to-red-700 text-white rounded-xl p-6 augment-transition opacity-0 transform translate-y-4 shadow-xl">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center mr-3">
                            🚀
                        </div>
                        <h3 class="text-xl font-bold">Augment</h3>
                    </div>
                    <p class="text-red-100 mb-6">Assistant IA pour le développement Laravel</p>
                    
                    <div class="space-y-3">
                        <div class="bg-white/20 rounded-lg p-3">
                            <div class="flex items-center gap-2 mb-2">
                                <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                                <span class="text-sm font-medium">Statut: Connecté</span>
                            </div>
                            <p class="text-xs text-red-100">Prêt à vous aider</p>
                        </div>
                        
                        <div class="bg-white/20 rounded-lg p-3">
                            <h4 class="text-sm font-medium mb-2">🔧 Fonctionnalités</h4>
                            <ul class="text-xs text-red-100 space-y-1">
                                <li>• Génération de code</li>
                                <li>• Débogage intelligent</li>
                                <li>• Optimisation</li>
                                <li>• Documentation</li>
                            </ul>
                        </div>
                        
                        <div class="bg-white/20 rounded-lg p-3">
                            <h4 class="text-sm font-medium mb-2">📊 Activité récente</h4>
                            <div class="text-xs text-red-100 space-y-1">
                                <div class="flex justify-between">
                                    <span>Requêtes traitées</span>
                                    <span class="font-medium">127</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Temps de réponse</span>
                                    <span class="font-medium">0.3s</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mt-8">
            <h3 class="text-lg font-medium mb-3 text-blue-900 dark:text-blue-100">📋 Instructions</h3>
            <p class="text-blue-800 dark:text-blue-200 mb-3">
                Cliquez sur le bouton "Déplacer Augment" pour voir le composant se déplacer de droite à gauche et vice versa.
            </p>
            <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <li>• Le composant Augment commence à droite</li>
                <li>• L'animation de transition dure 0.5 seconde</li>
                <li>• Le texte du bouton change selon la position actuelle</li>
                <li>• Une animation de feedback visuel accompagne le déplacement</li>
            </ul>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const toggleButton = document.getElementById('toggleAugment');
            const layoutContainer = document.getElementById('layoutContainer');
            const augmentComponent = document.getElementById('augmentComponent');
            let isAugmentOnLeft = false;

            toggleButton.addEventListener('click', function() {
                if (isAugmentOnLeft) {
                    // Déplacer Augment vers la droite
                    layoutContainer.style.flexDirection = 'row';
                    augmentComponent.style.order = '2';
                    toggleButton.textContent = 'Déplacer Augment à gauche';
                    isAugmentOnLeft = false;
                } else {
                    // Déplacer Augment vers la gauche
                    layoutContainer.style.flexDirection = 'row';
                    augmentComponent.style.order = '-1';
                    toggleButton.textContent = 'Déplacer Augment à droite';
                    isAugmentOnLeft = true;
                }
                
                // Animation de feedback
                augmentComponent.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    augmentComponent.style.transform = 'scale(1)';
                }, 150);
            });

            // Animation d'entrée
            setTimeout(() => {
                augmentComponent.style.opacity = '1';
                augmentComponent.style.transform = 'translateY(0)';
            }, 500);
        });
    </script>
</body>
</html>
